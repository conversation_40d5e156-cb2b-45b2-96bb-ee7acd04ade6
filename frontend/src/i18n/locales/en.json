{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "upload": "Upload", "download": "Download", "search": "Search", "settings": "Settings", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "confirm": "Confirm", "file": "File", "folder": "Folder"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "email": "Email", "name": "Full Name", "phone": "Phone Number", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "sendResetLink": "Send Reset Link", "loginSuccess": "Login successful!", "registerSuccess": "Registration successful! Please check your email to verify your account.", "logoutSuccess": "Logout successful!", "invalidCredentials": "Invalid username or password", "userExists": "User already exists", "passwordTooShort": "Password must be at least 6 characters", "passwordWeak": "Weak password", "passwordMedium": "Medium strength password", "passwordStrong": "Strong password", "passwordMismatch": "Passwords do not match", "emailRequired": "Email is required", "usernameRequired": "Username is required", "nameRequired": "Full name is required", "processing": "Processing...", "fillAllFields": "Please fill in all required fields", "failed": "Authentication failed", "networkError": "Network error. Please try again.", "passwordsMatch": "Passwords match ✓", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "changePasswordSuccess": "Password changed successfully", "changePasswordFailed": "Failed to change password", "currentPasswordIncorrect": "Current password is incorrect", "newPasswordSameAsCurrent": "New password must be different from current password", "passwordChangeDescription": "Update your account password for better security"}, "header": {"brand": "TeleStore", "tagline": "Your secure file storage solution", "searchPlaceholder": "Search files and folders...", "search": "Search...", "notifications": "Notifications", "help": "Help", "profile": "Profile", "darkMode": "Dark Mode", "lightMode": "Light Mode", "settings": "Settings", "signOut": "Sign out"}, "files": {"upload": "Upload Files", "createFolder": "Create Folder", "fileName": "File Name", "fileSize": "File Size", "dateModified": "Date Modified", "owner": "Owner", "type": "Type", "noFiles": "No files found", "uploadSuccess": "File uploaded successfully", "uploadError": "Failed to upload file", "downloadError": "Failed to download file", "deleteSuccess": "File deleted successfully", "deleteError": "Failed to delete file", "renameSuccess": "File renamed successfully", "renameError": "Failed to rename file"}, "preview": {"title": "Preview", "loading": "Loading preview...", "close": "Close", "downloadToView": "Download to View", "downloadInstead": "Download Instead", "downloadToExtract": "Download to extract and view contents", "cannotPreview": "This file type cannot be previewed in browser", "browserNotSupported": "Your browser does not support the video tag", "audioNotSupported": "Your browser does not support the audio tag", "pdfNotAvailable": "PDF preview is not available in your browser. Please download the file to view it.", "previewHere": "Preview Here", "openInNewTab": "Open in New Tab", "opening": "Opening...", "previewOptions": "Preview Options", "googleDocsViewer": "Google Docs Viewer (primary)", "microsoftOffice": "Microsoft Office Online (fallback)", "downloadLocal": "Download for local viewing", "currentlyUsing": "Currently using Microsoft Office Online viewer", "archiveFile": "Archive File", "pdfDocument": "PDF Document", "size": "Size"}, "folders": {"createFolder": "Create Folder", "folderName": "Folder Name", "createSuccess": "Folder created successfully", "createError": "Failed to create folder", "deleteSuccess": "Folder deleted successfully", "deleteError": "Failed to delete folder", "renameSuccess": "Folder renamed successfully", "renameError": "Failed to rename folder"}, "settings": {"title": "Settings", "language": "Language", "theme": "Theme", "profile": "Profile", "account": "Account", "security": "Security", "notifications": "Notifications", "storage": "Storage", "about": "About", "languageChanged": "Language changed successfully", "themeChanged": "Theme changed successfully", "english": "English", "vietnamese": "Tiếng <PERSON>", "chinese": "中文", "light": "Light", "dark": "Dark", "system": "System", "profileSecondary": "Manage your profile information", "securitySecondary": "Password and security settings", "notificationsSecondary": "Notification preferences", "storageSecondary": "Storage usage and limits", "aboutSecondary": "About TeleStore"}, "sidebar": {"home": "Home", "allFiles": "All files", "photos": "Photos", "shared": "Shared", "fileRequests": "File requests", "deletedFiles": "Deleted files", "quickAccess": "Quick access", "starred": "Starred", "recent": "Recent", "soon": "Soon"}, "breadcrumbs": {"home": "Home", "searching": "Searching...", "searchResults": "Search results for"}, "actionBar": {"uploadFiles": "Upload files", "upload": "Upload", "createFolder": "Create folder", "folder": "Folder", "getApp": "Get the app", "transferCopy": "Transfer a copy", "share": "Share", "copyLink": "Copy link", "shareFolder": "Share folder", "download": "Download", "move": "Move", "select": "Select", "cancel": "Cancel", "selectAll": "Select All", "selectedItems": "{{count}} selected"}, "storage": {"title": "Storage", "loading": "Loading storage...", "used": "Used", "available": "Available", "total": "Total", "files": "Files", "folders": "Folders", "refresh": "Refresh storage info", "details": "Storage Details", "usage": "Storage Usage", "almostFull": "⚠️ Storage almost full! Consider deleting unused files."}, "emailVerification": {"title": "Email Verification Required", "message": "Please check your email and click the verification link to activate your account.", "resendButton": "<PERSON><PERSON><PERSON>", "sending": "Sending...", "success": "Verification email sent successfully!", "error": "Failed to send verification email", "networkError": "Network error. Please try again.", "verifying": "Verifying your email...", "verifyingMessage": "Please wait while we verify your email address.", "verified": "Email Verified!", "verificationSuccess": "Email verified successfully!", "redirectMessage": "You will be redirected to login page in a few seconds...", "goToLogin": "Go to Login", "verificationFailed": "Verification Failed", "backToLogin": "Back to Login", "invalidLink": "Invalid verification link"}, "snackbar": {"invalidFolderName": "Invalid folder name", "folderCreated": "Folder created successfully", "invalidName": "Invalid name", "itemRenamed": "Item renamed successfully", "itemDeleted": "Item deleted successfully", "itemMoved": "<PERSON><PERSON> moved successfully", "itemsMoved": "Items moved successfully", "bulkMoveCompleted": "Bulk move completed: {{successful}} items moved successfully{{failed}}"}, "uploadDialog": {"title": "Upload Files", "selected": "selected", "mobileNoticeTitle": "Mobile Device Notice", "mobileNoticeDesc": "File sizes may appear different on mobile devices due to browser limitations. The actual uploaded file will maintain its original size.", "selectedFiles": "Selected files:", "addMore": "Add More", "cancel": "Cancel", "uploading": "Uploading...", "upload": "Upload"}, "createFolderDialog": {"title": "Create New Folder", "folderName": "Folder Name", "cancel": "Cancel", "create": "Create"}, "renameDialog": {"title": "Rename {{type}}", "newName": "New Name", "cancel": "Cancel", "rename": "<PERSON><PERSON>"}, "deleteDialog": {"title": "Delete {{type}}", "confirmDelete": "Are you sure you want to delete \"{{name}}\"?", "deleteFolderWarning": "This will also delete all contents inside this folder.", "cancel": "Cancel", "delete": "Delete"}, "fileGrid": {"videoNotSupported": "Your browser does not support the video tag.", "audioNotSupported": "Your browser does not support the audio tag.", "size": "Size", "type": "Type", "archiveFile": "Archive File", "downloadToExtract": "Download to extract and view contents", "cannotPreview": "This file type cannot be previewed in browser", "downloadToView": "Download to View", "name": "Name", "whoCanAccess": "Who can access", "modified": "Modified", "onlyYou": "Only you"}, "moveDialog": {"title": "Move to Folder", "selectDestination": "Select a destination folder:", "move": "Move", "rootFolder": "Root Folder", "rootDescription": "Move to the main directory", "current": "Current", "folder": "Folder", "noFolders": "No folders available", "loadingFolders": "Loading folders...", "disabled": "Cannot move here"}}
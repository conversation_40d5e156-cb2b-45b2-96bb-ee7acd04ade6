import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Tab,
  Tabs,
  Alert,
  LinearProgress,
  FormHelperText,
  useTheme,
  ThemeProvider,
  createTheme,
  CssBaseline,
  useMediaQuery,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import {
  validatePassword,
  getPasswordStrengthText,
} from "../utils/passwordValidation";
import { useTranslation } from "react-i18next";

interface LoginPageProps {
  onLogin: (token: string, user: any) => void;
}

const LoginPageNew: React.FC<LoginPageProps> = ({ onLogin }) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { t } = useTranslation();
  const prefersDarkMode = useMediaQuery("(prefers-color-scheme: dark)");

  // Initialize theme from localStorage first, then fallback to system preference
  const getInitialTheme = (): "light" | "dark" => {
    const savedTheme = localStorage.getItem("theme");
    if (savedTheme === "light" || savedTheme === "dark") {
      return savedTheme;
    }
    return prefersDarkMode ? "dark" : "light";
  };

  const [mode] = useState<"light" | "dark">(getInitialTheme());
  const [tabValue, setTabValue] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<any>(null);
  const [passwordMatch, setPasswordMatch] = useState<boolean | null>(null);
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    confirmPassword: "",
    email: "",
    name: "",
    phone: "",
  });

  const theme = createTheme({
    palette: {
      mode,
      primary: {
        main: mode === "dark" ? "#60a5fa" : "#2563eb",
        light: mode === "dark" ? "#93c5fd" : "#3b82f6",
        dark: mode === "dark" ? "#2563eb" : "#1d4ed8",
      },
      background: {
        default: mode === "dark" ? "#0f172a" : "#f8fafc",
        paper: mode === "dark" ? "#1e293b" : "#ffffff",
      },
      text: {
        primary: mode === "dark" ? "#f1f5f9" : "#1e293b",
        secondary: mode === "dark" ? "#cbd5e1" : "#64748b",
      },
    },
  });

  useEffect(() => {
    // Check for OAuth errors in URL params
    const error = searchParams.get("error");
    if (error) {
      setError(decodeURIComponent(error));
    }
  }, [searchParams]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear errors when user starts typing
    if (error) setError("");
    if (success) setSuccess("");

    // Check password strength for register mode
    if (name === "password" && tabValue === 1) {
      if (value.length > 0) {
        const strength = validatePassword(value);
        setPasswordStrength(strength);
      } else {
        setPasswordStrength(null);
      }

      // Check password match if confirm password is filled
      if (formData.confirmPassword) {
        setPasswordMatch(value === formData.confirmPassword);
      }
    }

    // Check password confirmation match
    if (name === "confirmPassword" && tabValue === 1) {
      if (value.length > 0) {
        setPasswordMatch(value === formData.password);
      } else {
        setPasswordMatch(null);
      }
    }
  };

  const validateForm = (): boolean => {
    if (tabValue === 0) {
      // Login
      if (!formData.username || !formData.password) {
        setError(t('auth.fillAllFields'));
        return false;
      }
    } else {
      // Register
      if (
        !formData.username ||
        !formData.password ||
        !formData.email ||
        !formData.name
      ) {
        setError(t('auth.fillAllFields'));
        return false;
      }

      if (formData.password.length < 6) {
        setError(t('auth.passwordTooShort'));
        return false;
      }

      if (formData.password !== formData.confirmPassword) {
        setError(t('auth.passwordMismatch'));
        return false;
      }
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      const endpoint =
        tabValue === 0 ? "/api/v1.0/auth/login" : "/api/v1.0/auth/register";
      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.code === 200) {
        if (tabValue === 0) {
          // Login
          onLogin(data.data.token, data.data);
          navigate("/");
        } else {
          // Register
          setSuccess(t("auth.registerSuccess"));
          setTabValue(0); // Switch to login tab
          setFormData((prev) => ({
            ...prev,
            password: "",
            confirmPassword: "",
          }));
        }
      } else {
        setError(data.message?.body || data.message || t('auth.failed'));
      }
    } catch (error) {
      setError(t('auth.networkError'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box
        sx={{
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          bgcolor: "background.default",
          p: 2,
        }}
      >
        <Paper
          sx={{
            p: 4,
            maxWidth: 400,
            width: "100%",
            borderRadius: 2,
            boxShadow:
              mode === "dark"
                ? "0 4px 20px rgba(0,0,0,0.3)"
                : "0 4px 20px rgba(0,0,0,0.1)",
          }}
        >
          {/* Header */}
          <Box sx={{ textAlign: "center", mb: 3 }}>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 700,
                mb: 1,
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                backgroundClip: "text",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              TeleStore
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t("header.tagline")}
            </Typography>
          </Box>

          {/* Error/Success Messages */}
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          {/* Tabs */}
          <Tabs
            value={tabValue}
            onChange={(_, newValue) => setTabValue(newValue)}
            variant="fullWidth"
            sx={{ mb: 3 }}
          >
            <Tab label={t("auth.login")} />
            <Tab label={t("auth.register")} />
          </Tabs>

          {/* Form */}
          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label={t("auth.username")}
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              margin="normal"
              required
            />

            {tabValue === 1 && (
              <>
                <TextField
                  fullWidth
                  label={t("auth.email")}
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  margin="normal"
                  required
                />
                <TextField
                  fullWidth
                  label={t("auth.name")}
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  margin="normal"
                  required
                />
                <TextField
                  fullWidth
                  label={t("auth.phone")}
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  margin="normal"
                />
              </>
            )}

            <TextField
              fullWidth
              label={t("auth.password")}
              name="password"
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChange={handleInputChange}
              margin="normal"
              required
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            {/* Password Strength Indicator */}
            {tabValue === 1 && passwordStrength && (
              <Box sx={{ mt: 1, mb: 1 }}>
                <LinearProgress
                  variant="determinate"
                  value={(passwordStrength.score / 4) * 100}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: "rgba(0,0,0,0.1)",
                    "& .MuiLinearProgress-bar": {
                      backgroundColor: passwordStrength.color,
                      borderRadius: 3,
                    },
                  }}
                />
                <FormHelperText sx={{ color: passwordStrength.color, mt: 0.5 }}>
                  {getPasswordStrengthText(passwordStrength.label)}
                  {passwordStrength.feedback.length > 0 && (
                    <span> - {passwordStrength.feedback[0]}</span>
                  )}
                </FormHelperText>
              </Box>
            )}

            {tabValue === 1 && (
              <>
                <TextField
                  fullWidth
                  label={t("auth.confirmPassword")}
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  margin="normal"
                  required
                  error={passwordMatch === false}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() =>
                            setShowConfirmPassword(!showConfirmPassword)
                          }
                          edge="end"
                        >
                          {showConfirmPassword ? (
                            <VisibilityOff />
                          ) : (
                            <Visibility />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
                {passwordMatch === false && (
                  <FormHelperText error sx={{ mt: 0.5 }}>
                    {t("auth.passwordMismatch")}
                  </FormHelperText>
                )}
                {passwordMatch === true && (
                  <FormHelperText sx={{ mt: 0.5, color: "success.main" }}>
                    {t('auth.passwordsMatch')}
                  </FormHelperText>
                )}
              </>
            )}

            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={isLoading}
              sx={{ mt: 3, mb: 2, py: 1.5 }}
            >
              {isLoading
                ? t("auth.processing")
                : tabValue === 0
                ? t("auth.login")
                : t("auth.register")}
            </Button>
          </form>

          {/* OAuth Section */}
          {tabValue === 0 && (
            <>
              <Box sx={{ textAlign: 'center', my: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  {t('auth.orContinueWith')}
                </Typography>
              </Box>

              <Button
                fullWidth
                variant="outlined"
                disabled={isLoading}
                onClick={() => window.location.href = '/auth/google'}
                sx={{
                  mb: 1,
                  py: 1.5,
                  borderColor: mode === 'dark' ? '#4285f4' : '#4285f4',
                  color: mode === 'dark' ? '#4285f4' : '#4285f4',
                  '&:hover': {
                    borderColor: mode === 'dark' ? '#3367d6' : '#3367d6',
                    backgroundColor: mode === 'dark' ? 'rgba(66, 133, 244, 0.1)' : 'rgba(66, 133, 244, 0.05)',
                  },
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}
              >
                <svg width="18" height="18" viewBox="0 0 24 24">
                  <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                {t('auth.continueWithGoogle')}
              </Button>
            </>
          )}
        </Paper>
      </Box>
    </ThemeProvider>
  );
};

export default LoginPageNew;

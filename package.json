{"name": "visiobox", "version": "1.0.0", "description": "VisioBox - Advanced file storage and management system with visual preview capabilities", "main": "index.js", "scripts": {"setup": "./scripts/setup.sh", "test": "./scripts/test-system.sh", "start": "docker-compose up -d", "stop": "docker-compose down", "restart": "docker-compose restart", "logs": "docker-compose logs -f", "logs:backend": "docker-compose logs -f backend", "logs:frontend": "docker-compose logs -f frontend", "logs:db": "docker-compose logs -f mongodb", "logs:redis": "docker-compose logs -f redis", "build": "docker-compose build", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "clean": "docker-compose down -v && docker system prune -f"}, "keywords": ["telegram", "file-storage", "cloud-storage", "nodejs", "react", "mongodb", "redis", "docker"], "author": "TeleStore Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/tele-store.git"}, "bugs": {"url": "https://github.com/your-username/tele-store/issues"}, "homepage": "https://github.com/your-username/tele-store#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
# Duplicate Detection and Hash Analysis Implementation

## Overview

This implementation addresses the issues with file hash differences between mobile and desktop uploads and adds comprehensive duplicate detection functionality to the TeleStore system.

## Issues Addressed

1. **Hash differences between mobile and desktop uploads**: Enhanced hash calculation with debugging capabilities
2. **Missing duplicate detection**: Added folder-level duplicate checking during upload
3. **Hash analysis tools**: Created utilities to compare and analyze file hashes

## Changes Made

### 1. Enhanced Hash Calculation (`backend/lib/util/metadata.js`)

- **Improved `calculateFileHashes` function**:
  - Added consistent 64KB chunk reading for reliable hash calculation
  - Enhanced debugging information including file stats and processing time
  - Better error handling and logging

- **New utility functions**:
  - `checkForDuplicates()`: Check for duplicate files in a specific folder
  - `compareFileHashes()`: Compare two file hash objects with detailed analysis

### 2. Updated Upload Process (`backend/lib/routes/api/files/upload/v1.0.js`)

- **Moved hash calculation before Telegram upload**: Prevents unnecessary Telegram uploads for duplicates
- **Added duplicate detection**: Checks for existing files with same hash in the same folder
- **Force upload option**: Added `forceUpload` parameter to bypass duplicate detection
- **Enhanced logging**: Better debugging information for hash differences

### 3. New API Endpoints

#### Check Duplicate Endpoint (`/api/v1.0/files/check-duplicate`)
- **Method**: POST
- **Purpose**: Check if a file with given hashes already exists in a folder
- **Request body**:
  ```json
  {
    "md5": "file_md5_hash",
    "sha256": "file_sha256_hash", 
    "parentId": "folder_id_or_null"
  }
  ```
- **Response**: Duplicate file information and hash comparison

#### Hash Analysis Endpoint (`/api/v1.0/files/hash-analysis`)
- **Method**: POST
- **Purpose**: Compare hashes of multiple files for analysis
- **Request body**:
  ```json
  {
    "fileIds": ["file_id_1", "file_id_2", "file_id_3"]
  }
  ```
- **Response**: Detailed hash comparison and grouping analysis

### 4. Debug Tools

#### Hash Debug Script (`backend/test-hash-debug.js`)
- **Usage**: `node test-hash-debug.js <file1> [file2]`
- **Features**:
  - Analyze single file hash calculation
  - Compare two files for hash differences
  - Debug information about file processing

## How to Use

### 1. Testing Hash Calculation

```bash
# Analyze a single file
node backend/test-hash-debug.js /path/to/your/file.jpg

# Compare two files
node backend/test-hash-debug.js /path/to/file1.jpg /path/to/file2.jpg
```

### 2. Upload with Duplicate Detection

The upload process now automatically checks for duplicates:

```javascript
// Normal upload - will reject duplicates
const formData = new FormData();
formData.append('file', file);
formData.append('parentId', folderId);

// Force upload - bypasses duplicate detection
formData.append('forceUpload', 'true');
```

### 3. Check for Duplicates Before Upload

```javascript
// Check if file already exists
const response = await fetch('/api/v1.0/files/check-duplicate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    md5: 'calculated_md5_hash',
    sha256: 'calculated_sha256_hash',
    parentId: 'folder_id_or_null'
  })
});
```

### 4. Analyze Hash Differences

```javascript
// Compare multiple files
const response = await fetch('/api/v1.0/files/hash-analysis', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    fileIds: ['file_id_1', 'file_id_2', 'file_id_3']
  })
});
```

## Debugging Hash Differences

### Common Causes of Hash Differences

1. **File modification during upload**: Mobile apps may modify EXIF data or compress images
2. **Different encoding**: Browser vs mobile app file handling
3. **Temporary file processing**: Different platforms may process files differently

### Debug Process

1. **Use the debug script** to compare files uploaded from different sources
2. **Check upload metadata** for device/platform information
3. **Analyze EXIF data** to see if metadata was stripped or modified
4. **Compare file sizes** to detect compression or modification

### Enhanced Logging

The system now logs detailed information about:
- File hash calculation process
- Duplicate detection results
- Upload source (mobile/desktop/web)
- Device and platform information

## API Response Examples

### Duplicate Detection Response

```json
{
  "code": "INVALID_PARAMS",
  "message": "A file with identical content already exists in this folder",
  "data": {
    "duplicateFile": {
      "id": "existing_file_id",
      "originalFileName": "existing_file.jpg",
      "fileSize": 1024000,
      "uploadDate": "2024-01-01T00:00:00.000Z",
      "fileHash": {
        "md5": "abc123...",
        "sha256": "def456..."
      }
    },
    "uploadedFile": {
      "originalFileName": "new_file.jpg",
      "fileSize": 1024000,
      "fileHash": {
        "md5": "abc123...",
        "sha256": "def456..."
      }
    },
    "hashComparison": {
      "isIdentical": true,
      "md5Match": true,
      "sha256Match": true,
      "analysis": ["MD5 hashes match", "SHA256 hashes match"]
    },
    "canForceUpload": true
  }
}
```

## Next Steps

1. **Test with actual mobile uploads** to identify specific hash difference patterns
2. **Implement frontend handling** for duplicate detection responses
3. **Add user preferences** for duplicate handling (auto-replace, rename, etc.)
4. **Create duplicate management UI** for users to handle existing duplicates
5. **Add global duplicate detection** across all folders (optional feature)

## Configuration

No additional configuration is required. The duplicate detection is enabled by default and can be bypassed using the `forceUpload` parameter.
